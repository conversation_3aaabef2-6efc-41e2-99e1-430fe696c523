import { Metadata } from "next";

export const metadata: Metadata = {
  title: {
    template: "%s - Work Finder",
    default: "Authentication - Work Finder",
  },
  description: "Sign in or create your Work Finder account to access your dashboard and apply for jobs.",
};

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 opacity-50" />
      
      {/* Content */}
      <div className="relative">
        {children}
      </div>
      
      {/* Footer */}
      <footer className="relative mt-auto py-8">
        <div className="text-center text-sm text-gray-500">
          <p>
            © 2024 Work Finder. All rights reserved.
          </p>
          <div className="mt-2 space-x-4">
            <a href="/privacy" className="hover:text-gray-700">Privacy Policy</a>
            <span>•</span>
            <a href="/terms" className="hover:text-gray-700">Terms of Service</a>
            <span>•</span>
            <a href="/contact" className="hover:text-gray-700">Contact</a>
          </div>
        </div>
      </footer>
    </div>
  );
}
