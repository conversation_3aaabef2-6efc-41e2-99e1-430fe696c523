import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";

export const metadata: Metadata = {
  title: {
    template: "%s - Work Finder",
    default: "Authentication - Work Finder",
  },
  description:
    "Sign in or create your Work Finder account to access your dashboard and apply for jobs.",
};

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex">
      {/* Left Column - Form */}
      <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          {/* Logo */}
          <div className="mb-8">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-[#1967D2] rounded-lg flex items-center justify-center">
                <span className="text-white font-medium text-sm">WF</span>
              </div>
              <span className="text-[25px] font-medium text-[#202124] font-['Jost',sans-serif]">
                Work Finder
              </span>
            </Link>
          </div>

          {/* Form Content */}
          {children}

          {/* Footer */}
          <div className="mt-8 text-center text-sm text-gray-500">
            <p>© 2024 Work Finder. All rights reserved.</p>
            <div className="mt-2 space-x-4">
              <Link href="/privacy" className="hover:text-gray-700">
                Privacy Policy
              </Link>
              <span>•</span>
              <Link href="/terms" className="hover:text-gray-700">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Right Column - Image */}
      <div className="hidden lg:block relative w-0 flex-1">
        <Image
          className="absolute inset-0 h-full w-full object-cover"
          src="/auth/auth_bg.png"
          alt="Authentication background"
          fill
          priority
        />
      </div>
    </div>
  );
}
