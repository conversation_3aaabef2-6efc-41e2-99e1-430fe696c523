import type { <PERSON><PERSON><PERSON> } from "next";
import { Jo<PERSON> } from "next/font/google";
import { Toaster } from "sonner";
import { QueryProvider } from "@/components/providers/QueryProvider";
import { ConditionalLayout } from "@/components/layout";
import "./globals.css";

const jost = Jost({
  variable: "--font-jost",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Work Finder - Find Your Dream Job",
  description:
    "Discover thousands of job opportunities and connect with top employers. Your next career move starts here.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${jost.variable} antialiased font-jost`}>
        <QueryProvider>
          <ConditionalLayout>{children}</ConditionalLayout>
          <Toaster
            position="top-right"
            richColors
            closeButton
            duration={4000}
          />
        </QueryProvider>
      </body>
    </html>
  );
}
