import { <PERSON>Header } from "@/components/shared";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, DollarSign, BarChart3, Users } from "lucide-react";

export default function SalaryPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <PageHeader
          title="Salary Insights"
          description="Explore salary data and compensation trends across industries"
          breadcrumbs={[
            { label: "Home", href: "/" },
            { label: "Salary" }
          ]}
        />

        {/* Salary Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Salary</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$75,000</div>
              <p className="text-xs text-muted-foreground">
                +12% from last year
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Top Paying Role</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$150,000</div>
              <p className="text-xs text-muted-foreground">
                Senior Software Engineer
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Companies Surveyed</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,247</div>
              <p className="text-xs text-muted-foreground">
                Across all industries
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">+8.5%</div>
              <p className="text-xs text-muted-foreground">
                Year over year
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Salary by Industry */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Salary by Industry</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { industry: "Technology", salary: "$95,000", growth: "+15%" },
                { industry: "Finance", salary: "$85,000", growth: "+8%" },
                { industry: "Healthcare", salary: "$78,000", growth: "+12%" },
                { industry: "Marketing", salary: "$65,000", growth: "+6%" },
                { industry: "Design", salary: "$70,000", growth: "+10%" },
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-medium">{item.industry}</h3>
                    <p className="text-sm text-gray-600">Average annual salary</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold">{item.salary}</div>
                    <Badge variant="secondary" className="text-green-600">
                      {item.growth}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Coming Soon Features */}
        <Card>
          <CardHeader>
            <CardTitle>Coming Soon</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg bg-blue-50">
                <h3 className="font-medium mb-2">Salary Calculator</h3>
                <p className="text-sm text-gray-600">
                  Calculate your expected salary based on experience, location, and skills.
                </p>
              </div>
              <div className="p-4 border rounded-lg bg-green-50">
                <h3 className="font-medium mb-2">Compensation Trends</h3>
                <p className="text-sm text-gray-600">
                  Track salary trends and market changes in real-time.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
