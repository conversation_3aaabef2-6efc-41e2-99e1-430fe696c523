import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { jobsService, JobSearchParams, SaveJobRequest, JobApplication, JobAlert } from '@/lib/api';
import { queryKeys, cacheUtils } from '@/lib/react-query/client';
import { toast } from 'sonner';

// Jobs queries
export const useJobs = (params: JobSearchParams) => {
  return useQuery({
    queryKey: queryKeys.jobs.list(params),
    queryFn: () => jobsService.searchJobs(params),
    enabled: true,
  });
};

export const useJob = (id: string) => {
  return useQuery({
    queryKey: queryKeys.jobs.detail(id),
    queryFn: () => jobsService.getJobById(id),
    enabled: !!id,
  });
};

export const useFeaturedJobs = (limit?: number) => {
  return useQuery({
    queryKey: queryKeys.jobs.featured(limit),
    queryFn: () => jobsService.getFeaturedJobs(limit),
  });
};

export const useRecentJobs = (limit?: number) => {
  return useQuery({
    queryKey: queryKeys.jobs.recent(limit),
    queryFn: () => jobsService.getRecentJobs(limit),
  });
};

export const useSimilarJobs = (jobId: string, limit?: number) => {
  return useQuery({
    queryKey: queryKeys.jobs.similar(jobId, limit),
    queryFn: () => jobsService.getSimilarJobs(jobId, limit),
    enabled: !!jobId,
  });
};

export const useRecommendedJobs = (params?: any) => {
  return useQuery({
    queryKey: queryKeys.jobs.recommendations(params),
    queryFn: () => jobsService.getRecommendedJobs(params),
  });
};

export const useSavedJobs = (page?: number, limit?: number) => {
  return useQuery({
    queryKey: queryKeys.jobs.saved({ page, limit }),
    queryFn: () => jobsService.getSavedJobs(page, limit),
  });
};

export const useJobCategories = () => {
  return useQuery({
    queryKey: queryKeys.jobs.categories(),
    queryFn: () => jobsService.getJobCategories(),
    staleTime: 1000 * 60 * 30, // 30 minutes
  });
};

export const useJobLocations = (query?: string) => {
  return useQuery({
    queryKey: queryKeys.jobs.locations(query),
    queryFn: () => jobsService.getJobLocations(query),
    enabled: query !== undefined,
    staleTime: 1000 * 60 * 10, // 10 minutes
  });
};

export const usePopularSkills = (limit?: number) => {
  return useQuery({
    queryKey: queryKeys.jobs.skills(limit),
    queryFn: () => jobsService.getPopularSkills(limit),
    staleTime: 1000 * 60 * 30, // 30 minutes
  });
};

export const useJobAlerts = () => {
  return useQuery({
    queryKey: queryKeys.jobs.alerts(),
    queryFn: () => jobsService.getJobAlerts(),
  });
};

export const useSearchSuggestions = (query: string) => {
  return useQuery({
    queryKey: queryKeys.jobs.suggestions(query),
    queryFn: () => jobsService.getSearchSuggestions(query),
    enabled: query.length > 2,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

export const useJobStats = (jobId: string) => {
  return useQuery({
    queryKey: queryKeys.jobs.stats(jobId),
    queryFn: () => jobsService.getJobStats(jobId),
    enabled: !!jobId,
  });
};

export const useJobApplicationStatus = (jobId: string) => {
  return useQuery({
    queryKey: queryKeys.jobs.applicationStatus(jobId),
    queryFn: () => jobsService.checkApplicationStatus(jobId),
    enabled: !!jobId,
  });
};

// Jobs mutations
export const useSaveJob = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: SaveJobRequest) => jobsService.saveJob(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs.saved() });
      toast.success('Job saved successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to save job');
    },
  });
};

export const useUnsaveJob = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (jobId: string) => jobsService.unsaveJob(jobId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs.saved() });
      toast.success('Job removed from saved jobs');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to remove job');
    },
  });
};

export const useApplyToJob = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: JobApplication) => jobsService.applyToJob(data),
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.applications.all });
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs.applicationStatus(variables.jobId) });
      toast.success('Application submitted successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to submit application');
    },
  });
};

export const useCreateJobAlert = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: Omit<JobAlert, 'id'>) => jobsService.createJobAlert(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs.alerts() });
      toast.success('Job alert created successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create job alert');
    },
  });
};

export const useUpdateJobAlert = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<JobAlert> }) => 
      jobsService.updateJobAlert(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs.alerts() });
      toast.success('Job alert updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update job alert');
    },
  });
};

export const useDeleteJobAlert = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => jobsService.deleteJobAlert(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs.alerts() });
      toast.success('Job alert deleted successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete job alert');
    },
  });
};

export const useToggleJobAlert = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => jobsService.toggleJobAlert(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs.alerts() });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to toggle job alert');
    },
  });
};

export const useReportJob = () => {
  return useMutation({
    mutationFn: ({ jobId, reason, description }: { jobId: string; reason: string; description?: string }) =>
      jobsService.reportJob(jobId, reason, description),
    onSuccess: () => {
      toast.success('Job reported successfully. Thank you for your feedback.');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to report job');
    },
  });
};
