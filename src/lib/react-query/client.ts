import { QueryClient, DefaultOptions } from '@tanstack/react-query';

// Default query options
const queryConfig: DefaultOptions = {
  queries: {
    // Time in milliseconds that unused/inactive cache data remains in memory
    gcTime: 1000 * 60 * 5, // 5 minutes
    
    // Time in milliseconds that the data is considered fresh
    staleTime: 1000 * 60 * 1, // 1 minute
    
    // Number of times to retry failed requests
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors (client errors)
      if (error?.statusCode >= 400 && error?.statusCode < 500) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
    
    // Retry delay function
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    
    // Refetch on window focus in production
    refetchOnWindowFocus: process.env.NODE_ENV === 'production',
    
    // Refetch on reconnect
    refetchOnReconnect: true,
    
    // Refetch on mount if data is stale
    refetchOnMount: true,
  },
  mutations: {
    // Retry mutations once
    retry: 1,
    
    // Retry delay for mutations
    retryDelay: 1000,
  },
};

// Create query client
export const queryClient = new QueryClient({
  defaultOptions: queryConfig,
});

// Query keys factory for consistent key management
export const queryKeys = {
  // Auth
  auth: {
    currentUser: ['auth', 'currentUser'] as const,
    checkEmail: (email: string) => ['auth', 'checkEmail', email] as const,
  },
  
  // Jobs
  jobs: {
    all: ['jobs'] as const,
    lists: () => [...queryKeys.jobs.all, 'list'] as const,
    list: (params: any) => [...queryKeys.jobs.lists(), params] as const,
    details: () => [...queryKeys.jobs.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.jobs.details(), id] as const,
    featured: (limit?: number) => [...queryKeys.jobs.all, 'featured', limit] as const,
    recent: (limit?: number) => [...queryKeys.jobs.all, 'recent', limit] as const,
    similar: (jobId: string, limit?: number) => [...queryKeys.jobs.all, 'similar', jobId, limit] as const,
    recommendations: (params?: any) => [...queryKeys.jobs.all, 'recommendations', params] as const,
    saved: (params?: any) => [...queryKeys.jobs.all, 'saved', params] as const,
    categories: () => [...queryKeys.jobs.all, 'categories'] as const,
    locations: (query?: string) => [...queryKeys.jobs.all, 'locations', query] as const,
    skills: (limit?: number) => [...queryKeys.jobs.all, 'skills', limit] as const,
    alerts: () => [...queryKeys.jobs.all, 'alerts'] as const,
    suggestions: (query: string) => [...queryKeys.jobs.all, 'suggestions', query] as const,
    stats: (jobId: string) => [...queryKeys.jobs.all, 'stats', jobId] as const,
    applicationStatus: (jobId: string) => [...queryKeys.jobs.all, 'applicationStatus', jobId] as const,
  },
  
  // Companies
  companies: {
    all: ['companies'] as const,
    lists: () => [...queryKeys.companies.all, 'list'] as const,
    list: (params: any) => [...queryKeys.companies.lists(), params] as const,
    details: () => [...queryKeys.companies.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.companies.details(), id] as const,
    bySlug: (slug: string) => [...queryKeys.companies.all, 'slug', slug] as const,
    featured: (limit?: number) => [...queryKeys.companies.all, 'featured', limit] as const,
    topRated: (limit?: number) => [...queryKeys.companies.all, 'topRated', limit] as const,
    mostJobs: (limit?: number) => [...queryKeys.companies.all, 'mostJobs', limit] as const,
    jobs: (companyId: string, params?: any) => [...queryKeys.companies.all, companyId, 'jobs', params] as const,
    reviews: (companyId: string, params?: any) => [...queryKeys.companies.all, companyId, 'reviews', params] as const,
    followed: (params?: any) => [...queryKeys.companies.all, 'followed', params] as const,
    industries: () => [...queryKeys.companies.all, 'industries'] as const,
    sizes: () => [...queryKeys.companies.all, 'sizes'] as const,
    locations: (query?: string) => [...queryKeys.companies.all, 'locations', query] as const,
    suggestions: (query: string) => [...queryKeys.companies.all, 'suggestions', query] as const,
    stats: (companyId: string) => [...queryKeys.companies.all, 'stats', companyId] as const,
  },
  
  // Applications
  applications: {
    all: ['applications'] as const,
    lists: () => [...queryKeys.applications.all, 'list'] as const,
    list: (params: any) => [...queryKeys.applications.lists(), params] as const,
    details: () => [...queryKeys.applications.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.applications.details(), id] as const,
    byStatus: (status: string, params?: any) => [...queryKeys.applications.all, 'status', status, params] as const,
    recent: (limit?: number) => [...queryKeys.applications.all, 'recent', limit] as const,
    stats: () => [...queryKeys.applications.all, 'stats'] as const,
    timeline: (id: string) => [...queryKeys.applications.all, 'timeline', id] as const,
    reminders: () => [...queryKeys.applications.all, 'reminders'] as const,
    analytics: (dateFrom?: string, dateTo?: string) => [...queryKeys.applications.all, 'analytics', dateFrom, dateTo] as const,
    check: (jobId: string) => [...queryKeys.applications.all, 'check', jobId] as const,
    suggestions: (limit?: number) => [...queryKeys.applications.all, 'suggestions', limit] as const,
  },
} as const;

// Utility functions for cache management
export const cacheUtils = {
  // Invalidate all queries for a specific entity
  invalidateJobs: () => queryClient.invalidateQueries({ queryKey: queryKeys.jobs.all }),
  invalidateCompanies: () => queryClient.invalidateQueries({ queryKey: queryKeys.companies.all }),
  invalidateApplications: () => queryClient.invalidateQueries({ queryKey: queryKeys.applications.all }),
  invalidateAuth: () => queryClient.invalidateQueries({ queryKey: queryKeys.auth.currentUser }),
  
  // Remove specific queries from cache
  removeJob: (id: string) => queryClient.removeQueries({ queryKey: queryKeys.jobs.detail(id) }),
  removeCompany: (id: string) => queryClient.removeQueries({ queryKey: queryKeys.companies.detail(id) }),
  removeApplication: (id: string) => queryClient.removeQueries({ queryKey: queryKeys.applications.detail(id) }),
  
  // Prefetch data
  prefetchJob: (id: string) => queryClient.prefetchQuery({
    queryKey: queryKeys.jobs.detail(id),
    queryFn: () => import('../api').then(({ jobsService }) => jobsService.getJobById(id)),
  }),
  
  prefetchCompany: (id: string) => queryClient.prefetchQuery({
    queryKey: queryKeys.companies.detail(id),
    queryFn: () => import('../api').then(({ companiesService }) => companiesService.getCompanyById(id)),
  }),
  
  // Clear all cache
  clearAll: () => queryClient.clear(),
};
