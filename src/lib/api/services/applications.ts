import { apiClient, buildQueryString } from '../client';
import { PaginatedResponse } from '@/types/api';

// Application types
export interface Application {
  id: string;
  jobId: string;
  userId: string;
  status: 'pending' | 'reviewing' | 'interview' | 'offer' | 'rejected' | 'withdrawn';
  coverLetter?: string;
  resumeUrl?: string;
  additionalDocuments?: string[];
  answers?: Record<string, any>;
  notes?: string;
  appliedAt: string;
  updatedAt: string;
  
  // Related data
  job?: {
    id: string;
    title: string;
    company: {
      id: string;
      name: string;
      logo?: string;
    };
    location: string;
    jobType: string;
    salary?: {
      min: number;
      max: number;
      currency: string;
    };
  };
}

export interface ApplicationTimeline {
  id: string;
  applicationId: string;
  status: string;
  title: string;
  description?: string;
  createdAt: string;
  createdBy?: {
    id: string;
    name: string;
    role: 'system' | 'recruiter' | 'candidate';
  };
}

export interface ApplicationFilters {
  status?: string[];
  dateFrom?: string;
  dateTo?: string;
  jobType?: string[];
  companyId?: string;
}

export interface ApplicationSearchParams {
  page?: number;
  limit?: number;
  sort?: 'date' | 'status' | 'company' | 'position';
  order?: 'asc' | 'desc';
  filters?: ApplicationFilters;
}

export interface CreateApplicationRequest {
  jobId: string;
  coverLetter?: string;
  resumeUrl?: string;
  additionalDocuments?: string[];
  answers?: Record<string, any>;
}

export interface UpdateApplicationRequest {
  coverLetter?: string;
  resumeUrl?: string;
  additionalDocuments?: string[];
  answers?: Record<string, any>;
  notes?: string;
}

export interface ApplicationStats {
  total: number;
  pending: number;
  reviewing: number;
  interview: number;
  offer: number;
  rejected: number;
  withdrawn: number;
  responseRate: number;
  averageResponseTime: number;
}

// Applications API Service
export class ApplicationsService {
  private readonly baseUrl = '/applications';

  // Get user applications with filters and pagination
  async getApplications(params: ApplicationSearchParams = {}): Promise<PaginatedResponse<Application>> {
    const query = buildQueryString({
      page: params.page,
      limit: params.limit,
      sort: params.sort,
      order: params.order,
      ...params.filters,
    });
    
    return apiClient.getPaginated<Application>(`${this.baseUrl}?${query}`);
  }

  // Get application by ID
  async getApplicationById(id: string): Promise<Application> {
    return apiClient.get<Application>(`${this.baseUrl}/${id}`);
  }

  // Create new application
  async createApplication(data: CreateApplicationRequest): Promise<Application> {
    return apiClient.post<Application>(`${this.baseUrl}`, data);
  }

  // Update application
  async updateApplication(id: string, data: UpdateApplicationRequest): Promise<Application> {
    return apiClient.patch<Application>(`${this.baseUrl}/${id}`, data);
  }

  // Withdraw application
  async withdrawApplication(id: string, reason?: string): Promise<{ message: string }> {
    return apiClient.patch<{ message: string }>(`${this.baseUrl}/${id}/withdraw`, {
      reason,
    });
  }

  // Delete application
  async deleteApplication(id: string): Promise<{ message: string }> {
    return apiClient.delete<{ message: string }>(`${this.baseUrl}/${id}`);
  }

  // Get application timeline
  async getApplicationTimeline(id: string): Promise<ApplicationTimeline[]> {
    return apiClient.get<ApplicationTimeline[]>(`${this.baseUrl}/${id}/timeline`);
  }

  // Add note to application
  async addApplicationNote(id: string, note: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/${id}/notes`, {
      note,
    });
  }

  // Get application statistics
  async getApplicationStats(): Promise<ApplicationStats> {
    return apiClient.get<ApplicationStats>(`${this.baseUrl}/stats`);
  }

  // Get applications by status
  async getApplicationsByStatus(
    status: string, 
    page: number = 1, 
    limit: number = 20
  ): Promise<PaginatedResponse<Application>> {
    const query = buildQueryString({ status, page, limit });
    return apiClient.getPaginated<Application>(`${this.baseUrl}/status?${query}`);
  }

  // Get recent applications
  async getRecentApplications(limit: number = 10): Promise<Application[]> {
    const query = buildQueryString({ limit, sort: 'date', order: 'desc' });
    return apiClient.get<Application[]>(`${this.baseUrl}/recent?${query}`);
  }

  // Bulk operations
  async bulkUpdateStatus(applicationIds: string[], status: string): Promise<{ message: string; updated: number }> {
    return apiClient.patch<{ message: string; updated: number }>(`${this.baseUrl}/bulk/status`, {
      applicationIds,
      status,
    });
  }

  async bulkWithdraw(applicationIds: string[], reason?: string): Promise<{ message: string; updated: number }> {
    return apiClient.patch<{ message: string; updated: number }>(`${this.baseUrl}/bulk/withdraw`, {
      applicationIds,
      reason,
    });
  }

  async bulkDelete(applicationIds: string[]): Promise<{ message: string; deleted: number }> {
    return apiClient.delete<{ message: string; deleted: number }>(`${this.baseUrl}/bulk`, {
      data: { applicationIds },
    });
  }

  // Export applications
  async exportApplications(
    format: 'csv' | 'xlsx' | 'pdf',
    filters?: ApplicationFilters
  ): Promise<{ downloadUrl: string }> {
    const query = buildQueryString({ format, ...filters });
    return apiClient.get<{ downloadUrl: string }>(`${this.baseUrl}/export?${query}`);
  }

  // Application reminders
  async setApplicationReminder(
    id: string, 
    reminderDate: string, 
    message?: string
  ): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/${id}/reminder`, {
      reminderDate,
      message,
    });
  }

  async getApplicationReminders(): Promise<Array<{
    id: string;
    applicationId: string;
    reminderDate: string;
    message?: string;
    isCompleted: boolean;
  }>> {
    return apiClient.get(`${this.baseUrl}/reminders`);
  }

  async completeApplicationReminder(reminderId: string): Promise<{ message: string }> {
    return apiClient.patch<{ message: string }>(`${this.baseUrl}/reminders/${reminderId}/complete`);
  }

  // Application analytics
  async getApplicationAnalytics(dateFrom?: string, dateTo?: string): Promise<{
    totalApplications: number;
    applicationsByStatus: Record<string, number>;
    applicationsByMonth: Array<{ month: string; count: number }>;
    topCompanies: Array<{ company: string; count: number }>;
    topJobTypes: Array<{ jobType: string; count: number }>;
    responseRateByCompany: Array<{ company: string; responseRate: number }>;
    averageResponseTime: number;
  }> {
    const query = buildQueryString({ dateFrom, dateTo });
    return apiClient.get(`${this.baseUrl}/analytics${query ? `?${query}` : ''}`);
  }

  // Check application status for a job
  async checkApplicationStatus(jobId: string): Promise<{
    hasApplied: boolean;
    application?: Application;
  }> {
    return apiClient.get(`${this.baseUrl}/check/${jobId}`);
  }

  // Get application suggestions (jobs to apply to)
  async getApplicationSuggestions(limit: number = 10): Promise<Array<{
    job: any;
    matchScore: number;
    reasons: string[];
  }>> {
    const query = buildQueryString({ limit });
    return apiClient.get(`${this.baseUrl}/suggestions?${query}`);
  }
}

// Export singleton instance
export const applicationsService = new ApplicationsService();
