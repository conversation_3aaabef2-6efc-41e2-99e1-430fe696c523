import { apiClient, buildQueryString } from '../client';
import { PaginatedResponse } from '@/types/api';

// Company types (you might want to move these to types/company.ts)
export interface Company {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  website?: string;
  description?: string;
  industry: string;
  size: string;
  founded?: number;
  headquarters?: {
    city: string;
    state?: string;
    country: string;
  };
  rating?: number;
  reviewCount?: number;
  jobCount?: number;
  benefits?: string[];
  culture?: string[];
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
    instagram?: string;
  };
  featured?: boolean;
  verified?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CompanyReview {
  id: string;
  companyId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  content: string;
  pros?: string;
  cons?: string;
  advice?: string;
  position?: string;
  department?: string;
  employmentType?: 'full-time' | 'part-time' | 'contract' | 'internship';
  workLocation?: 'office' | 'remote' | 'hybrid';
  isCurrentEmployee: boolean;
  isVerified: boolean;
  helpful: number;
  notHelpful: number;
  createdAt: string;
  updatedAt: string;
}

export interface CompanyFilters {
  industry?: string[];
  size?: string[];
  location?: string;
  rating?: number;
  hasJobs?: boolean;
  verified?: boolean;
  featured?: boolean;
}

export interface CompanySearchParams {
  q?: string;
  page?: number;
  limit?: number;
  sort?: 'relevance' | 'name' | 'rating' | 'jobs' | 'founded';
  order?: 'asc' | 'desc';
  filters?: CompanyFilters;
}

export interface CompanyReviewFilters {
  rating?: number;
  department?: string;
  position?: string;
  employmentType?: string;
  workLocation?: string;
  isCurrentEmployee?: boolean;
}

export interface CompanyReviewParams {
  page?: number;
  limit?: number;
  sort?: 'date' | 'rating' | 'helpful';
  order?: 'asc' | 'desc';
  filters?: CompanyReviewFilters;
}

export interface CreateReviewRequest {
  companyId: string;
  rating: number;
  title: string;
  content: string;
  pros?: string;
  cons?: string;
  advice?: string;
  position?: string;
  department?: string;
  employmentType?: 'full-time' | 'part-time' | 'contract' | 'internship';
  workLocation?: 'office' | 'remote' | 'hybrid';
  isCurrentEmployee: boolean;
}

// Companies API Service
export class CompaniesService {
  private readonly baseUrl = '/companies';

  // Search companies with filters and pagination
  async searchCompanies(params: CompanySearchParams): Promise<PaginatedResponse<Company>> {
    const query = buildQueryString({
      q: params.q,
      page: params.page,
      limit: params.limit,
      sort: params.sort,
      order: params.order,
      ...params.filters,
    });
    
    return apiClient.getPaginated<Company>(`${this.baseUrl}?${query}`);
  }

  // Get company by ID or slug
  async getCompanyById(id: string): Promise<Company> {
    return apiClient.get<Company>(`${this.baseUrl}/${id}`);
  }

  async getCompanyBySlug(slug: string): Promise<Company> {
    return apiClient.get<Company>(`${this.baseUrl}/slug/${slug}`);
  }

  // Get featured companies
  async getFeaturedCompanies(limit: number = 10): Promise<Company[]> {
    const query = buildQueryString({ limit, featured: true });
    return apiClient.get<Company[]>(`${this.baseUrl}/featured?${query}`);
  }

  // Get top-rated companies
  async getTopRatedCompanies(limit: number = 10): Promise<Company[]> {
    const query = buildQueryString({ limit, sort: 'rating', order: 'desc' });
    return apiClient.get<Company[]>(`${this.baseUrl}/top-rated?${query}`);
  }

  // Get companies with most jobs
  async getCompaniesWithMostJobs(limit: number = 10): Promise<Company[]> {
    const query = buildQueryString({ limit, sort: 'jobs', order: 'desc' });
    return apiClient.get<Company[]>(`${this.baseUrl}/most-jobs?${query}`);
  }

  // Get company jobs
  async getCompanyJobs(companyId: string, page: number = 1, limit: number = 20): Promise<PaginatedResponse<any>> {
    const query = buildQueryString({ page, limit });
    return apiClient.getPaginated(`${this.baseUrl}/${companyId}/jobs?${query}`);
  }

  // Get company reviews
  async getCompanyReviews(
    companyId: string, 
    params: CompanyReviewParams = {}
  ): Promise<PaginatedResponse<CompanyReview>> {
    const query = buildQueryString({
      page: params.page,
      limit: params.limit,
      sort: params.sort,
      order: params.order,
      ...params.filters,
    });
    
    return apiClient.getPaginated<CompanyReview>(`${this.baseUrl}/${companyId}/reviews?${query}`);
  }

  // Create company review
  async createReview(data: CreateReviewRequest): Promise<CompanyReview> {
    return apiClient.post<CompanyReview>(`${this.baseUrl}/reviews`, data);
  }

  // Update company review
  async updateReview(reviewId: string, data: Partial<CreateReviewRequest>): Promise<CompanyReview> {
    return apiClient.patch<CompanyReview>(`${this.baseUrl}/reviews/${reviewId}`, data);
  }

  // Delete company review
  async deleteReview(reviewId: string): Promise<{ message: string }> {
    return apiClient.delete<{ message: string }>(`${this.baseUrl}/reviews/${reviewId}`);
  }

  // Mark review as helpful/not helpful
  async markReviewHelpful(reviewId: string, helpful: boolean): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/reviews/${reviewId}/helpful`, {
      helpful,
    });
  }

  // Follow/unfollow company
  async followCompany(companyId: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/${companyId}/follow`);
  }

  async unfollowCompany(companyId: string): Promise<{ message: string }> {
    return apiClient.delete<{ message: string }>(`${this.baseUrl}/${companyId}/follow`);
  }

  // Get followed companies
  async getFollowedCompanies(page: number = 1, limit: number = 20): Promise<PaginatedResponse<Company>> {
    const query = buildQueryString({ page, limit });
    return apiClient.getPaginated<Company>(`${this.baseUrl}/followed?${query}`);
  }

  // Get company statistics
  async getCompanyStats(companyId: string): Promise<{
    totalJobs: number;
    activeJobs: number;
    totalReviews: number;
    averageRating: number;
    ratingDistribution: Record<string, number>;
    followerCount: number;
  }> {
    return apiClient.get(`${this.baseUrl}/${companyId}/stats`);
  }

  // Get company industries
  async getIndustries(): Promise<Array<{ id: string; name: string; count: number }>> {
    return apiClient.get<Array<{ id: string; name: string; count: number }>>(
      `${this.baseUrl}/industries`
    );
  }

  // Get company sizes
  async getCompanySizes(): Promise<Array<{ id: string; name: string; count: number }>> {
    return apiClient.get<Array<{ id: string; name: string; count: number }>>(
      `${this.baseUrl}/sizes`
    );
  }

  // Get company locations
  async getCompanyLocations(query?: string): Promise<Array<{ city: string; state: string; country: string; count: number }>> {
    const params = query ? buildQueryString({ q: query }) : '';
    return apiClient.get<Array<{ city: string; state: string; country: string; count: number }>>(
      `${this.baseUrl}/locations${params ? `?${params}` : ''}`
    );
  }

  // Search company suggestions
  async getCompanySuggestions(query: string): Promise<Array<{ id: string; name: string; logo?: string }>> {
    const params = buildQueryString({ q: query });
    return apiClient.get<Array<{ id: string; name: string; logo?: string }>>(
      `${this.baseUrl}/suggestions?${params}`
    );
  }

  // Report company or review
  async reportCompany(companyId: string, reason: string, description?: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/${companyId}/report`, {
      reason,
      description,
    });
  }

  async reportReview(reviewId: string, reason: string, description?: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/reviews/${reviewId}/report`, {
      reason,
      description,
    });
  }
}

// Export singleton instance
export const companiesService = new CompaniesService();
