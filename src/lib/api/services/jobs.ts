import { apiClient, buildQueryString } from '../client';
import { PaginatedResponse } from '@/types/api';
import { Job } from '@/types/job';

// Job search and filter types
export interface JobFilters {
  location?: string;
  jobType?: string[];
  experienceLevel?: string[];
  salaryMin?: number;
  salaryMax?: number;
  companySize?: string[];
  industry?: string[];
  skills?: string[];
  remote?: boolean;
  postedWithin?: string;
  companyId?: string;
}

export interface JobSearchParams {
  q?: string;
  page?: number;
  limit?: number;
  sort?: 'relevance' | 'date' | 'salary' | 'company';
  order?: 'asc' | 'desc';
  filters?: JobFilters;
}

export interface SaveJobRequest {
  jobId: string;
  notes?: string;
}

export interface JobApplication {
  jobId: string;
  coverLetter?: string;
  resumeUrl?: string;
  additionalDocuments?: string[];
  answers?: Record<string, any>;
}

export interface JobAlert {
  id?: string;
  name: string;
  searchParams: JobSearchParams;
  frequency: 'daily' | 'weekly' | 'monthly';
  isActive: boolean;
}

export interface JobRecommendationParams {
  limit?: number;
  excludeApplied?: boolean;
  excludeSaved?: boolean;
}

// Jobs API Service
export class JobsService {
  private readonly baseUrl = '/jobs';

  // Search jobs with filters and pagination
  async searchJobs(params: JobSearchParams): Promise<PaginatedResponse<Job>> {
    const query = buildQueryString({
      q: params.q,
      page: params.page,
      limit: params.limit,
      sort: params.sort,
      order: params.order,
      ...params.filters,
    });
    
    return apiClient.getPaginated<Job>(`${this.baseUrl}?${query}`);
  }

  // Get job by ID
  async getJobById(id: string): Promise<Job> {
    return apiClient.get<Job>(`${this.baseUrl}/${id}`);
  }

  // Get featured jobs
  async getFeaturedJobs(limit: number = 10): Promise<Job[]> {
    const query = buildQueryString({ limit, featured: true });
    return apiClient.get<Job[]>(`${this.baseUrl}/featured?${query}`);
  }

  // Get recent jobs
  async getRecentJobs(limit: number = 10): Promise<Job[]> {
    const query = buildQueryString({ limit, sort: 'date', order: 'desc' });
    return apiClient.get<Job[]>(`${this.baseUrl}/recent?${query}`);
  }

  // Get similar jobs
  async getSimilarJobs(jobId: string, limit: number = 5): Promise<Job[]> {
    const query = buildQueryString({ limit });
    return apiClient.get<Job[]>(`${this.baseUrl}/${jobId}/similar?${query}`);
  }

  // Get job recommendations for user
  async getRecommendedJobs(params: JobRecommendationParams = {}): Promise<Job[]> {
    const query = buildQueryString(params);
    return apiClient.get<Job[]>(`${this.baseUrl}/recommendations?${query}`);
  }

  // Save/bookmark a job
  async saveJob(data: SaveJobRequest): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/save`, data);
  }

  // Unsave/unbookmark a job
  async unsaveJob(jobId: string): Promise<{ message: string }> {
    return apiClient.delete<{ message: string }>(`${this.baseUrl}/save/${jobId}`);
  }

  // Get saved jobs
  async getSavedJobs(page: number = 1, limit: number = 20): Promise<PaginatedResponse<Job>> {
    const query = buildQueryString({ page, limit });
    return apiClient.getPaginated<Job>(`${this.baseUrl}/saved?${query}`);
  }

  // Apply to a job
  async applyToJob(data: JobApplication): Promise<{ message: string; applicationId: string }> {
    return apiClient.post<{ message: string; applicationId: string }>(
      `${this.baseUrl}/apply`,
      data
    );
  }

  // Check if user has applied to job
  async checkApplicationStatus(jobId: string): Promise<{ hasApplied: boolean; applicationId?: string }> {
    return apiClient.get<{ hasApplied: boolean; applicationId?: string }>(
      `${this.baseUrl}/${jobId}/application-status`
    );
  }

  // Get job statistics
  async getJobStats(jobId: string): Promise<{
    totalApplications: number;
    viewCount: number;
    saveCount: number;
    averageRating: number;
  }> {
    return apiClient.get(`${this.baseUrl}/${jobId}/stats`);
  }

  // Report a job
  async reportJob(jobId: string, reason: string, description?: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/${jobId}/report`, {
      reason,
      description,
    });
  }

  // Get job categories
  async getJobCategories(): Promise<Array<{ id: string; name: string; count: number }>> {
    return apiClient.get<Array<{ id: string; name: string; count: number }>>(
      `${this.baseUrl}/categories`
    );
  }

  // Get job locations
  async getJobLocations(query?: string): Promise<Array<{ city: string; state: string; country: string; count: number }>> {
    const params = query ? buildQueryString({ q: query }) : '';
    return apiClient.get<Array<{ city: string; state: string; country: string; count: number }>>(
      `${this.baseUrl}/locations${params ? `?${params}` : ''}`
    );
  }

  // Get popular skills
  async getPopularSkills(limit: number = 50): Promise<Array<{ skill: string; count: number }>> {
    const query = buildQueryString({ limit });
    return apiClient.get<Array<{ skill: string; count: number }>>(
      `${this.baseUrl}/skills?${query}`
    );
  }

  // Job Alerts Management
  async createJobAlert(data: Omit<JobAlert, 'id'>): Promise<JobAlert> {
    return apiClient.post<JobAlert>(`${this.baseUrl}/alerts`, data);
  }

  async getJobAlerts(): Promise<JobAlert[]> {
    return apiClient.get<JobAlert[]>(`${this.baseUrl}/alerts`);
  }

  async updateJobAlert(id: string, data: Partial<JobAlert>): Promise<JobAlert> {
    return apiClient.patch<JobAlert>(`${this.baseUrl}/alerts/${id}`, data);
  }

  async deleteJobAlert(id: string): Promise<{ message: string }> {
    return apiClient.delete<{ message: string }>(`${this.baseUrl}/alerts/${id}`);
  }

  async toggleJobAlert(id: string): Promise<JobAlert> {
    return apiClient.patch<JobAlert>(`${this.baseUrl}/alerts/${id}/toggle`);
  }

  // Search suggestions
  async getSearchSuggestions(query: string): Promise<{
    jobs: string[];
    companies: string[];
    locations: string[];
    skills: string[];
  }> {
    const params = buildQueryString({ q: query });
    return apiClient.get(`${this.baseUrl}/search/suggestions?${params}`);
  }
}

// Export singleton instance
export const jobsService = new JobsService();
