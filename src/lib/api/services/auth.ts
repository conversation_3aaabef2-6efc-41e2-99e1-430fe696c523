import { apiClient, buildQueryString } from '../client';
import { User } from '@/types/user';

// Auth request/response types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  accountType: 'job-seeker' | 'employer';
  termsAccepted: boolean;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface VerifyEmailRequest {
  token: string;
}

export interface ResendVerificationRequest {
  email: string;
}

// Auth API Service
export class AuthService {
  private readonly baseUrl = '/auth';

  // Login user
  async login(data: LoginRequest): Promise<AuthResponse> {
    return apiClient.post<AuthResponse>(`${this.baseUrl}/login`, data);
  }

  // Register user
  async register(data: RegisterRequest): Promise<AuthResponse> {
    return apiClient.post<AuthResponse>(`${this.baseUrl}/register`, data);
  }

  // Logout user
  async logout(): Promise<void> {
    return apiClient.post<void>(`${this.baseUrl}/logout`);
  }

  // Refresh access token
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    return apiClient.post<AuthResponse>(`${this.baseUrl}/refresh`, {
      refreshToken,
    });
  }

  // Get current user profile
  async getCurrentUser(): Promise<User> {
    return apiClient.get<User>(`${this.baseUrl}/me`);
  }

  // Forgot password
  async forgotPassword(data: ForgotPasswordRequest): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/forgot-password`, data);
  }

  // Reset password
  async resetPassword(data: ResetPasswordRequest): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/reset-password`, data);
  }

  // Change password (authenticated user)
  async changePassword(data: ChangePasswordRequest): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/change-password`, data);
  }

  // Verify email
  async verifyEmail(data: VerifyEmailRequest): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/verify-email`, data);
  }

  // Resend verification email
  async resendVerification(data: ResendVerificationRequest): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/resend-verification`, data);
  }

  // Check if email exists
  async checkEmail(email: string): Promise<{ exists: boolean }> {
    const query = buildQueryString({ email });
    return apiClient.get<{ exists: boolean }>(`${this.baseUrl}/check-email?${query}`);
  }

  // Social login (Google, Facebook, etc.)
  async socialLogin(provider: string, token: string): Promise<AuthResponse> {
    return apiClient.post<AuthResponse>(`${this.baseUrl}/social/${provider}`, {
      token,
    });
  }

  // Get social login URL
  getSocialLoginUrl(provider: string, redirectUrl?: string): string {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
    const params = redirectUrl ? `?redirect=${encodeURIComponent(redirectUrl)}` : '';
    return `${baseUrl}${this.baseUrl}/social/${provider}${params}`;
  }

  // Update user profile
  async updateProfile(data: Partial<User>): Promise<User> {
    return apiClient.patch<User>(`${this.baseUrl}/profile`, data);
  }

  // Upload profile picture
  async uploadProfilePicture(file: File, onProgress?: (progress: number) => void): Promise<{ url: string }> {
    return apiClient.uploadFile<{ url: string }>(
      `${this.baseUrl}/profile/picture`,
      file,
      onProgress
    );
  }

  // Delete account
  async deleteAccount(password: string): Promise<{ message: string }> {
    return apiClient.delete<{ message: string }>(`${this.baseUrl}/account`, {
      data: { password },
    });
  }

  // Enable/disable two-factor authentication
  async enableTwoFactor(): Promise<{ qrCode: string; secret: string }> {
    return apiClient.post<{ qrCode: string; secret: string }>(`${this.baseUrl}/2fa/enable`);
  }

  async disableTwoFactor(code: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/2fa/disable`, { code });
  }

  async verifyTwoFactor(code: string): Promise<{ message: string }> {
    return apiClient.post<{ message: string }>(`${this.baseUrl}/2fa/verify`, { code });
  }
}

// Export singleton instance
export const authService = new AuthService();
