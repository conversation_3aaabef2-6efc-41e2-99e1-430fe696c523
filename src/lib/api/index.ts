// Export API client and utilities
export { api<PERSON>lient, TokenManager, buildQueryString } from './client';

// Export all services
export { authService } from './services/auth';
export { jobsService } from './services/jobs';
export { companiesService } from './services/companies';
export { applicationsService } from './services/applications';

// Export types from services
export type {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  ChangePasswordRequest,
} from './services/auth';

export type {
  JobFilters,
  JobSearchParams,
  SaveJobRequest,
  JobApplication,
  JobAlert,
  JobRecommendationParams,
} from './services/jobs';

export type {
  Company,
  CompanyReview,
  CompanyFilters,
  CompanySearchParams,
  CompanyReviewFilters,
  CompanyReviewParams,
  CreateReviewRequest,
} from './services/companies';

export type {
  Application,
  ApplicationTimeline,
  ApplicationFilters,
  ApplicationSearchParams,
  CreateApplicationRequest,
  UpdateApplicationRequest,
  ApplicationStats,
} from './services/applications';

// Centralized API object for easy access
export const api = {
  auth: authService,
  jobs: jobsService,
  companies: companiesService,
  applications: applicationsService,
} as const;

// API configuration
export const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
  timeout: 30000,
} as const;
