"use client";

import { usePathname } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/layout";

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname();
  
  // Check if current path is an auth route
  const isAuthRoute = pathname.startsWith('/login') || 
                     pathname.startsWith('/register') || 
                     pathname.startsWith('/forgot-password') ||
                     pathname.startsWith('/verify-email') ||
                     pathname.startsWith('/reset-password');

  // For auth routes, return children without Header/Footer
  if (isAuthRoute) {
    return <>{children}</>;
  }

  // For all other routes, include <PERSON><PERSON> and <PERSON><PERSON>
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 relative pt-16 lg:pt-0">{children}</main>
      <Footer />
    </div>
  );
}
