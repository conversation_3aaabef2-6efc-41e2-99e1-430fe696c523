# API Integration Guide

## 📋 Tổng Quan

Dự án Work Finder đã được setup với một API client hoàn chỉnh sử dụng Axios và React Query để tương tác với backend. Guide này sẽ hướng dẫn cách sử dụng và mở rộng API integration.

## 🏗️ Cấu Trúc API Client

### Base API Client (`/lib/api/client.ts`)

```typescript
// Khởi tạo API client
import { apiClient } from '@/lib/api';

// Sử dụng trực tiếp
const response = await apiClient.get('/users');
const user = await apiClient.post('/users', userData);
```

### Features Chính

#### 1. 🔐 Token Management
```typescript
import { TokenManager } from '@/lib/api';

// Lưu tokens sau khi login
TokenManager.setTokens(accessToken, refreshToken);

// Lấy current token
const token = TokenManager.getAccessToken();

// Clear tokens khi logout
TokenManager.clearTokens();
```

#### 2. 🔄 Auto Token Refresh
- Tự động refresh token khi expired
- Retry failed requests với token mới
- Redirect to login nếu refresh thất bại

#### 3. 📤 File Upload
```typescript
// Upload file với progress tracking
const result = await apiClient.uploadFile(
  '/upload',
  file,
  (progress) => console.log(`Upload: ${progress}%`)
);
```

## 🎯 API Services

### 1. Authentication Service

```typescript
import { authService } from '@/lib/api';

// Login
const authResponse = await authService.login({
  email: '<EMAIL>',
  password: 'password123'
});

// Register
const newUser = await authService.register({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  password: 'password123',
  confirmPassword: 'password123',
  accountType: 'job-seeker',
  termsAccepted: true
});

// Get current user
const currentUser = await authService.getCurrentUser();
```

### 2. Jobs Service

```typescript
import { jobsService } from '@/lib/api';

// Search jobs
const jobs = await jobsService.searchJobs({
  q: 'developer',
  page: 1,
  limit: 20,
  filters: {
    location: 'San Francisco',
    jobType: ['full-time'],
    salaryMin: 80000,
    remote: true
  }
});

// Get job details
const job = await jobsService.getJobById('job-id-123');

// Save job
await jobsService.saveJob({
  jobId: 'job-id-123',
  notes: 'Interesting position'
});

// Apply to job
await jobsService.applyToJob({
  jobId: 'job-id-123',
  coverLetter: 'Dear hiring manager...',
  resumeUrl: 'https://example.com/resume.pdf'
});
```

### 3. Companies Service

```typescript
import { companiesService } from '@/lib/api';

// Search companies
const companies = await companiesService.searchCompanies({
  q: 'tech',
  filters: {
    industry: ['technology'],
    size: ['51-200'],
    location: 'San Francisco'
  }
});

// Get company details
const company = await companiesService.getCompanyById('company-id-123');

// Follow company
await companiesService.followCompany('company-id-123');

// Create review
await companiesService.createReview({
  companyId: 'company-id-123',
  rating: 5,
  title: 'Great place to work',
  content: 'Amazing culture and benefits...',
  isCurrentEmployee: true
});
```

### 4. Applications Service

```typescript
import { applicationsService } from '@/lib/api';

// Get user applications
const applications = await applicationsService.getApplications({
  page: 1,
  limit: 20,
  filters: {
    status: ['pending', 'reviewing']
  }
});

// Get application timeline
const timeline = await applicationsService.getApplicationTimeline('app-id-123');

// Withdraw application
await applicationsService.withdrawApplication('app-id-123', 'Found another opportunity');
```

## 🎣 React Query Hooks

### Jobs Hooks

```typescript
import { useJobs, useSaveJob, useApplyToJob } from '@/hooks/api/useJobs';

function JobsPage() {
  // Fetch jobs với caching
  const { data: jobs, isLoading, error } = useJobs({
    q: 'developer',
    page: 1,
    limit: 20
  });

  // Save job mutation
  const saveJobMutation = useSaveJob();
  
  const handleSaveJob = (jobId: string) => {
    saveJobMutation.mutate({ jobId, notes: 'Interesting' });
  };

  // Apply to job mutation
  const applyMutation = useApplyToJob();
  
  const handleApply = (jobId: string) => {
    applyMutation.mutate({
      jobId,
      coverLetter: 'Dear hiring manager...'
    });
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {jobs?.data.map(job => (
        <JobCard 
          key={job.id} 
          job={job}
          onSave={() => handleSaveJob(job.id)}
          onApply={() => handleApply(job.id)}
        />
      ))}
    </div>
  );
}
```

### Authentication Hooks

```typescript
import { useLogin, useRegister, useCurrentUser } from '@/hooks/api/useAuth';

function LoginForm() {
  const loginMutation = useLogin();
  
  const handleSubmit = (data: LoginRequest) => {
    loginMutation.mutate(data, {
      onSuccess: (response) => {
        // Redirect to dashboard
        router.push('/dashboard');
      },
      onError: (error) => {
        // Show error message
        toast.error(error.message);
      }
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  );
}
```

## 🔧 Configuration

### Environment Variables

```env
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:8000/api
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### API Client Configuration

```typescript
// lib/api/client.ts
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
};
```

### React Query Configuration

```typescript
// lib/react-query/client.ts
const queryConfig: DefaultOptions = {
  queries: {
    gcTime: 1000 * 60 * 5, // 5 minutes
    staleTime: 1000 * 60 * 1, // 1 minute
    retry: (failureCount, error: any) => {
      if (error?.statusCode >= 400 && error?.statusCode < 500) {
        return false;
      }
      return failureCount < 3;
    },
  },
};
```

## 🎯 Query Keys Management

```typescript
import { queryKeys } from '@/lib/react-query/client';

// Consistent query keys
const jobsQuery = useQuery({
  queryKey: queryKeys.jobs.list({ q: 'developer' }),
  queryFn: () => jobsService.searchJobs({ q: 'developer' })
});

// Invalidate related queries
const saveJobMutation = useMutation({
  mutationFn: jobsService.saveJob,
  onSuccess: () => {
    queryClient.invalidateQueries({ 
      queryKey: queryKeys.jobs.saved() 
    });
  }
});
```

## 🔄 Cache Management

```typescript
import { cacheUtils } from '@/lib/react-query/client';

// Invalidate all jobs queries
cacheUtils.invalidateJobs();

// Remove specific job from cache
cacheUtils.removeJob('job-id-123');

// Prefetch job data
cacheUtils.prefetchJob('job-id-123');

// Clear all cache
cacheUtils.clearAll();
```

## 🚨 Error Handling

### Global Error Handling

```typescript
// API client tự động handle:
// - 401: Auto token refresh
// - 4xx: Client errors (no retry)
// - 5xx: Server errors (retry với backoff)

// Custom error handling trong components
const { data, error } = useJobs(params);

if (error) {
  // Error object có structure:
  // {
  //   message: string,
  //   statusCode: number,
  //   error: string,
  //   timestamp: string
  // }
}
```

### Mutation Error Handling

```typescript
const mutation = useMutation({
  mutationFn: jobsService.saveJob,
  onError: (error: ApiError) => {
    if (error.statusCode === 401) {
      // Redirect to login
      router.push('/login');
    } else {
      // Show error toast
      toast.error(error.message);
    }
  }
});
```

## 📊 Loading States

### Query Loading States

```typescript
const { data, isLoading, isFetching, isError } = useJobs(params);

// isLoading: true khi lần đầu fetch
// isFetching: true khi đang fetch (including background refetch)
// isError: true khi có error
```

### Mutation Loading States

```typescript
const mutation = useMutation({
  mutationFn: jobsService.saveJob
});

// mutation.isPending: true khi đang execute
// mutation.isSuccess: true khi thành công
// mutation.isError: true khi có error
```

## 🔧 Extending API Client

### Adding New Service

```typescript
// 1. Create service file
// lib/api/services/notifications.ts
export class NotificationsService {
  private readonly baseUrl = '/notifications';

  async getNotifications(): Promise<Notification[]> {
    return apiClient.get<Notification[]>(this.baseUrl);
  }

  async markAsRead(id: string): Promise<void> {
    return apiClient.patch(`${this.baseUrl}/${id}/read`);
  }
}

export const notificationsService = new NotificationsService();

// 2. Add to main API export
// lib/api/index.ts
export { notificationsService } from './services/notifications';

// 3. Create custom hooks
// hooks/api/useNotifications.ts
export const useNotifications = () => {
  return useQuery({
    queryKey: ['notifications'],
    queryFn: () => notificationsService.getNotifications()
  });
};
```

### Adding New Query Keys

```typescript
// lib/react-query/client.ts
export const queryKeys = {
  // ... existing keys
  notifications: {
    all: ['notifications'] as const,
    lists: () => [...queryKeys.notifications.all, 'list'] as const,
    detail: (id: string) => [...queryKeys.notifications.all, 'detail', id] as const,
  },
};
```

## 🧪 Testing API Integration

### Mocking API Calls

```typescript
// __tests__/api/jobs.test.ts
import { jobsService } from '@/lib/api';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('JobsService', () => {
  it('should fetch jobs', async () => {
    const mockJobs = [{ id: '1', title: 'Developer' }];
    mockedAxios.get.mockResolvedValue({ data: { data: mockJobs } });

    const result = await jobsService.searchJobs({ q: 'developer' });
    
    expect(result).toEqual(mockJobs);
    expect(mockedAxios.get).toHaveBeenCalledWith('/jobs?q=developer');
  });
});
```

### Testing React Query Hooks

```typescript
// __tests__/hooks/useJobs.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useJobs } from '@/hooks/api/useJobs';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useJobs', () => {
  it('should fetch jobs', async () => {
    const { result } = renderHook(
      () => useJobs({ q: 'developer' }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toBeDefined();
  });
});
```

---

**API Integration Status**: ✅ Complete  
**Ready for**: Backend connection và feature implementation
